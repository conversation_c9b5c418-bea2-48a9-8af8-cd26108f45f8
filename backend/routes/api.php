<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use Illuminate\Support\Facades\Broadcast;
use App\Http\Controllers\MessageController;
use Illuminate\Support\Facades\Log;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');
Broadcast::routes(['middleware' => ['auth:sanctum']]);

// Add a specific route for broadcasting auth with logging
Route::post('/broadcasting/auth', function (Request $request) {
    Log::info('Broadcasting auth request received', ['user' => $request->user() ? $request->user()->id : 'none']);
    return Broadcast::auth($request);
})->middleware('auth:sanctum');

// Message routes
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/messages', [MessageController::class, 'send']);
    Route::get('/messages/history', [MessageController::class, 'history']);
});
